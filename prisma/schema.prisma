generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String       @id @default(cuid())
  name          String?
  email         String?      @unique
  emailVerified DateTime?
  image         String?
  role          UserRole     @default(PLAYER)
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  accounts      Account[]
  predictions   Prediction[]
  sessions      Session[]

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

model Driver {
  id                     String       @id @default(cuid())
  name                   String
  team                   String
  number                 Int          @unique
  active                 Boolean      @default(true)
  createdAt              DateTime     @default(now())
  updatedAt              DateTime     @updatedAt
  firstPlaceResults      Event[]      @relation("FirstPlaceResult")
  secondPlaceResults     Event[]      @relation("SecondPlaceResult")
  thirdPlaceResults      Event[]      @relation("ThirdPlaceResult")
  firstPlacePredictions  Prediction[] @relation("FirstPlace")
  secondPlacePredictions Prediction[] @relation("SecondPlace")
  thirdPlacePredictions  Prediction[] @relation("ThirdPlace")

  @@map("drivers")
}

model Event {
  id            String       @id @default(cuid())
  name          String
  type          EventType
  date          DateTime
  closingDate   DateTime
  status        EventStatus  @default(UPCOMING)
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  firstPlaceId  String?
  secondPlaceId String?
  thirdPlaceId  String?
  firstPlace    Driver?      @relation("FirstPlaceResult", fields: [firstPlaceId], references: [id])
  secondPlace   Driver?      @relation("SecondPlaceResult", fields: [secondPlaceId], references: [id])
  thirdPlace    Driver?      @relation("ThirdPlaceResult", fields: [thirdPlaceId], references: [id])
  predictions   Prediction[]

  @@map("events")
}

model Prediction {
  id            String   @id @default(cuid())
  userId        String
  eventId       String
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  firstPlaceId  String
  secondPlaceId String
  thirdPlaceId  String
  points        Float?
  event         Event    @relation(fields: [eventId], references: [id], onDelete: Cascade)
  firstPlace    Driver   @relation("FirstPlace", fields: [firstPlaceId], references: [id])
  secondPlace   Driver   @relation("SecondPlace", fields: [secondPlaceId], references: [id])
  thirdPlace    Driver   @relation("ThirdPlace", fields: [thirdPlaceId], references: [id])
  user          User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, eventId])
  @@map("predictions")
}

enum UserRole {
  ADMIN
  PLAYER
}

enum EventType {
  RACE
  SPRINT
}

enum EventStatus {
  UPCOMING
  CLOSED
  COMPLETED
}
