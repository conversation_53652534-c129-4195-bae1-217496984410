@import "tailwindcss";

:root {
  --f1-red: #E10600;
  --f1-dark: #1C1C1C;
  --f1-silver: #C0C0C0;
}

/* Custom FantaF1 styles */
html {
  scroll-behavior: smooth;
}

body {
  background-color: rgb(249 250 251);
  color: rgb(17 24 39);
}

.btn-primary {
  background-color: var(--f1-red);
  color: white;
  font-weight: bold;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: rgb(185 28 28);
}

.btn-secondary {
  background-color: rgb(229 231 235);
  color: rgb(31 41 55);
  font-weight: bold;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  transition: background-color 0.2s;
}

.btn-secondary:hover {
  background-color: rgb(209 213 219);
}

/* Login page animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(135deg, var(--f1-red), #dc2626);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass effect */
.glass {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  padding: 1.5rem;
  border: 1px solid rgb(229 231 235);
}

/* Mobile menu animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

.mobile-menu-enter {
  animation: slideInRight 0.3s ease-out;
}

.mobile-menu-exit {
  animation: slideOutRight 0.3s ease-in;
}

/* Touch-friendly styles */
@media (max-width: 768px) {
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  .touch-button {
    padding: 12px 16px;
    font-size: 16px;
  }
}

/* Mobile-specific utilities */
.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

.mobile-tap-highlight {
  -webkit-tap-highlight-color: rgba(225, 6, 0, 0.1);
}

.mobile-no-tap-highlight {
  -webkit-tap-highlight-color: transparent;
}

/* Prevent zoom on input focus (iOS) */
@media screen and (max-width: 767px) {
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="tel"],
  input[type="url"],
  input[type="search"],
  textarea,
  select {
    font-size: 16px !important;
  }
}

/* Safe area insets for notched devices */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

/* Responsive text utilities */
.text-responsive {
  font-size: clamp(0.875rem, 2.5vw, 1rem);
  line-height: 1.5;
}

.text-responsive-lg {
  font-size: clamp(1rem, 3vw, 1.25rem);
  line-height: 1.4;
}

.text-responsive-xl {
  font-size: clamp(1.25rem, 4vw, 1.5rem);
  line-height: 1.3;
}

.text-responsive-2xl {
  font-size: clamp(1.5rem, 5vw, 2rem);
  line-height: 1.2;
}

.text-responsive-3xl {
  font-size: clamp(1.875rem, 6vw, 2.5rem);
  line-height: 1.1;
}

/* Mobile typography scale */
@media (max-width: 767px) {
  h1 {
    font-size: clamp(1.75rem, 5vw, 2.25rem);
    line-height: 1.2;
    margin-bottom: 1rem;
  }

  h2 {
    font-size: clamp(1.5rem, 4vw, 1.875rem);
    line-height: 1.3;
    margin-bottom: 0.875rem;
  }

  h3 {
    font-size: clamp(1.25rem, 3.5vw, 1.5rem);
    line-height: 1.4;
    margin-bottom: 0.75rem;
  }

  h4 {
    font-size: clamp(1.125rem, 3vw, 1.25rem);
    line-height: 1.4;
    margin-bottom: 0.625rem;
  }

  p {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 1rem;
  }

  .text-sm {
    font-size: 0.875rem;
    line-height: 1.5;
  }

  .text-xs {
    font-size: 0.75rem;
    line-height: 1.4;
  }
}

/* Mobile-optimized spacing */
.space-mobile > * + * {
  margin-top: 1rem;
}

@media (min-width: 640px) {
  .space-mobile > * + * {
    margin-top: 1.5rem;
  }
}

/* Responsive padding utilities */
.p-mobile {
  padding: 1rem;
}

@media (min-width: 640px) {
  .p-mobile {
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .p-mobile {
    padding: 2rem;
  }
}

.px-mobile {
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .px-mobile {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .px-mobile {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.py-mobile {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

@media (min-width: 640px) {
  .py-mobile {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .py-mobile {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
}

/* Responsive margin utilities */
.m-mobile {
  margin: 1rem;
}

@media (min-width: 640px) {
  .m-mobile {
    margin: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .m-mobile {
    margin: 2rem;
  }
}

.mb-mobile {
  margin-bottom: 1rem;
}

@media (min-width: 640px) {
  .mb-mobile {
    margin-bottom: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .mb-mobile {
    margin-bottom: 2rem;
  }
}

/* Gesture support */
.swipe-container {
  touch-action: pan-x;
  user-select: none;
}

.no-scroll {
  overflow: hidden;
  position: fixed;
  width: 100%;
}

/* Touch interaction improvements */
.touch-action-manipulation {
  touch-action: manipulation;
}

.touch-action-pan-x {
  touch-action: pan-x;
}

.touch-action-pan-y {
  touch-action: pan-y;
}

.touch-action-none {
  touch-action: none;
}

/* Hover states for touch devices */
@media (hover: none) {
  .hover\:bg-gray-50:hover {
    background-color: transparent;
  }

  .hover\:bg-gray-100:hover {
    background-color: transparent;
  }

  .hover\:text-f1-red:hover {
    color: inherit;
  }
}

/* Active states for touch devices */
.touch-active:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

.touch-active-scale:active {
  transform: scale(0.95);
  transition: transform 0.1s ease;
}

/* Focus styles for touch navigation */
.touch-focus:focus {
  outline: 2px solid #E10600;
  outline-offset: 2px;
}

/* Smooth scrolling for mobile */
.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Pull to refresh indicator */
.pull-to-refresh {
  transition: transform 0.3s ease, background-color 0.3s ease;
}

.pull-to-refresh.refreshing {
  background: linear-gradient(to bottom, rgba(34, 197, 94, 0.1), transparent);
}

/* Swipe indicators */
.swipe-indicator {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.swipe-indicator.visible {
  opacity: 1;
}

.swipe-indicator.left {
  left: 20px;
}

.swipe-indicator.right {
  right: 20px;
}

.input-field {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid rgb(209 213 219);
  border-radius: 0.375rem;
  outline: none;
}

.input-field:focus {
  ring: 2px solid var(--f1-red);
  border-color: transparent;
}

/* Force visibility for prediction submit button */
.prediction-submit-button {
  background-color: var(--f1-red) !important;
  color: white !important;
  border: none !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.prediction-submit-button:disabled {
  background-color: #9CA3AF !important;
  color: white !important;
  cursor: not-allowed !important;
}
