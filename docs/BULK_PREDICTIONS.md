# Bulk Predictions Management Interface

## Overview

The Bulk Predictions interface is an admin-only tool designed for rapid data entry and management of user predictions across all races. This interface is optimized for importing historical prediction data and managing predictions efficiently.

## Access

- **URL**: `/admin/bulk-predictions`
- **Required Role**: ADMIN
- **Navigation**: Available in the admin sidebar as "Bulk Pronostici"

## Features

### 1. Race Selection
- View all races (past, present, future) with prediction counts
- Visual indicators for race status (Upcoming, Closed, Completed)
- Display race results for completed events
- No restrictions on editing completed races (admin override)

### 2. Predictions Grid
- Tabular view with users as rows and prediction positions as columns
- Real-time validation with visual status indicators:
  - ✅ Complete and valid predictions
  - ⚠️ Partial predictions (missing fields)
  - ❌ Invalid predictions (duplicate drivers)
  - ⭕ Empty predictions
- Optimized for keyboard navigation

### 3. Keyboard Navigation
- **Arrow Keys**: Navigate between cells
- **Tab/Shift+Tab**: Move forward/backward through fields
- **Enter**: Open dropdown for current cell
- Automatic focus management for rapid data entry

### 4. User Search & Filtering
- Real-time search by user name or email
- Shows filtered count vs total users
- Maintains search state during operations

### 5. Bulk Operations

#### Save All
- Validates all predictions before saving
- Shows progress indicator during save
- Only saves complete and valid predictions
- Provides detailed success/error feedback

#### Copy Predictions
- Copy all predictions from one race to another
- Select source race from dropdown
- Confirms overwrite of existing predictions
- Shows prediction counts for both source and target

#### Clear All Predictions
- Remove all predictions for the selected race
- Double confirmation required
- Shows count of predictions to be deleted
- Irreversible operation

### 6. Validation Rules
- Each prediction must have all three positions filled
- No duplicate drivers within a single prediction
- All drivers must exist in the database
- Real-time validation feedback

### 7. Error Handling & Notifications
- Toast notifications for all operations
- Detailed error messages with context
- Success confirmations with operation details
- Network error handling with retry suggestions

## Usage Workflow

### For Historical Data Import

1. **Select Target Race**: Choose the race you want to add predictions to
2. **Use Search**: Filter to specific users if needed
3. **Rapid Entry**: Use keyboard navigation to quickly fill predictions
4. **Bulk Save**: Save all valid predictions at once
5. **Verify**: Check the success notification and grid status

### For Data Migration

1. **Select Source Race**: Note the race with existing predictions
2. **Select Target Race**: Choose the destination race
3. **Copy Operation**: Use "Copia da altra gara" bulk operation
4. **Confirm**: Review the confirmation dialog carefully
5. **Verify**: Check that predictions were copied correctly

### For Data Cleanup

1. **Select Race**: Choose the race to clean up
2. **Clear Operation**: Use "Cancella tutti i pronostici"
3. **Double Confirm**: Confirm the irreversible operation
4. **Verify**: Check that all predictions were removed

## Performance Optimizations

- **Memoized Components**: Prevents unnecessary re-renders
- **Efficient State Management**: Minimal state updates
- **Keyboard Navigation**: Optimized for rapid data entry
- **Lazy Loading**: Components load only when needed
- **Progress Indicators**: Visual feedback for long operations

## Security Features

- **Admin-Only Access**: Strict role-based access control
- **Input Validation**: Server-side validation for all operations
- **Confirmation Dialogs**: Multiple confirmations for destructive operations
- **Audit Trail**: All operations are logged (via existing prediction system)

## Technical Details

### API Endpoints
- `GET /api/admin/bulk-predictions?eventId={id}` - Get predictions for race
- `POST /api/admin/bulk-predictions` - Bulk update/copy/clear operations
- `GET /api/admin/bulk-predictions/events` - Get all events

### Database Operations
- Uses Prisma transactions for data consistency
- Upsert operations for create/update predictions
- Cascade deletes handled properly
- Foreign key constraints enforced

### Error Scenarios Handled
- Network connectivity issues
- Invalid driver selections
- Duplicate driver assignments
- Missing required fields
- Database constraint violations
- Concurrent modification conflicts

## Best Practices

1. **Always backup** before bulk operations
2. **Test with small datasets** first
3. **Use search filtering** for large user bases
4. **Save frequently** during data entry
5. **Verify results** after bulk operations
6. **Double-check** before clearing predictions

## Troubleshooting

### Common Issues

**Predictions not saving**
- Check for validation errors (red status indicators)
- Ensure all three positions are filled
- Verify no duplicate drivers in same prediction

**Keyboard navigation not working**
- Click on a cell first to establish focus
- Ensure browser allows keyboard navigation
- Try refreshing the page if navigation breaks

**Copy operation failed**
- Verify source race has predictions
- Check target race exists and is accessible
- Ensure sufficient permissions

**Performance issues**
- Use search filtering to reduce visible users
- Save in smaller batches if needed
- Check network connectivity

### Support

For technical issues or feature requests, contact the development team or create an issue in the project repository.
